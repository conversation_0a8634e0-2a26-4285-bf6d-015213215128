import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFaceVideoCaptureBloc extends Mock implements FaceVideoCaptureBloc {}

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

void main() {
  group('[FACE_VERIFICATION] CameraPreviewWidget Video Path Tests', () {
    late MockFaceVideoCaptureBloc mockBloc;
    late MockVideoStorageRepository mockRepository;
    late Directory tempDir;

    setUpAll(() {
      registerFallbackValue(const InitializeCamera());
      registerFallbackValue(const VideoRecordingCompleted(videoPath: ''));
    });

    setUp(() {
      mockBloc = MockFaceVideoCaptureBloc();
      mockRepository = MockVideoStorageRepository();
      tempDir = Directory.systemTemp.createTempSync('video_path_test_');

      // Default mock behaviors
      when(() => mockBloc.state).thenReturn(const Initial());
      when(() => mockBloc.stream).thenAnswer((_) => const Stream.empty());
      when(() => mockRepository.currentVideoPath).thenReturn(null);
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<FaceVideoCaptureBloc>.value(value: mockBloc),
            RepositoryProvider<VideoStorageRepository>.value(
              value: mockRepository,
            ),
          ],
          child: const Scaffold(
            body: CameraPreviewWidget(),
          ),
        ),
      );
    }

    group('[FACE_VERIFICATION] Video Path Synchronization', () {
      testWidgets(
          'should get expected video path from repository when '
          'transitioning to recording', (tester) async {
        // Arrange
        const expectedPath = '/test/path/video.mp4';
        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);

        // Start with CameraReady state
        when(() => mockBloc.state).thenReturn(
          const CameraReady(
            canStartRecording: true,
          ),
        );

        // Create a stream that transitions to Recording state
        when(() => mockBloc.stream).thenAnswer(
          (_) => Stream.fromIterable([
            const Recording(
              elapsedTime: Duration.zero,
              remainingTime: Duration(seconds: 9),
            ),
          ]),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Initial build
        await tester.pump(); // Process the state change

        // Assert
        verify(() => mockRepository.currentVideoPath).called(greaterThan(0));
      });

      testWidgets('should handle null video path from repository gracefully',
          (tester) async {
        // Arrange
        when(() => mockRepository.currentVideoPath).thenReturn(null);

        // Start with CameraReady state
        when(() => mockBloc.state).thenReturn(
          const CameraReady(
            canStartRecording: true,
          ),
        );

        // Create a stream that transitions to Recording state
        when(() => mockBloc.stream).thenAnswer(
          (_) => Stream.fromIterable([
            const Recording(
              elapsedTime: Duration.zero,
              remainingTime: Duration(seconds: 9),
            ),
          ]),
        );

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Initial build
        await tester.pump(); // Process the state change

        verify(() => mockRepository.currentVideoPath).called(greaterThan(0));
      });
    });

    group('[FACE_VERIFICATION] Alternative File Handling', () {
      testWidgets(
          'should render widget without errors when repository has path',
          (tester) async {
        // Arrange
        const expectedPath = '/test/expected_video.mp4';

        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Verify the widget rendered successfully
        expect(find.byType(CameraPreviewWidget), findsOneWidget);
      });

      testWidgets('should handle file move failure gracefully', (tester) async {
        // Arrange
        const expectedPath = '/test/expected_video.mp4';

        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Verify the widget rendered successfully
        expect(find.byType(CameraPreviewWidget), findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Video Path Builder Integration', () {
      testWidgets('should render successfully with expected path available',
          (tester) async {
        // Arrange
        const expectedPath = '/test/path/video.mp4';
        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert - Widget should render without errors
        expect(find.byType(CameraPreviewWidget), findsOneWidget);
      });

      testWidgets('should render successfully when expected path is null',
          (tester) async {
        // Arrange
        when(() => mockRepository.currentVideoPath).thenReturn(null);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert - Widget should render without errors
        expect(find.byType(CameraPreviewWidget), findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Error Handling', () {
      testWidgets('should handle repository access errors gracefully',
          (tester) async {
        // Arrange
        when(() => mockRepository.currentVideoPath)
            .thenThrow(Exception('Repository error'));

        // Start with CameraReady state
        when(() => mockBloc.state).thenReturn(
          const CameraReady(
            canStartRecording: true,
          ),
        );

        // Create a stream that transitions to Recording state
        when(() => mockBloc.stream).thenAnswer(
          (_) => Stream.fromIterable([
            const Recording(
              elapsedTime: Duration.zero,
              remainingTime: Duration(seconds: 9),
            ),
          ]),
        );

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Initial build
        await tester.pump(); // Process the state change

        // Verify the repository was accessed despite the error
        verify(() => mockRepository.currentVideoPath).called(greaterThan(0));
      });
    });
  });
}
