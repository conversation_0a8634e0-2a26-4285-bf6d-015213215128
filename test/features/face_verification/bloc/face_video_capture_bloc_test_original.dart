import 'dart:io';

import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFaceDetectionRepository extends Mock
    implements FaceDetectionRepository {}

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

class MockVideoValidationService extends Mock
    implements VideoValidationService {}

/// Helper function to create a temporary video file for testing
Future<String> createTempVideoFile() async {
  final tempDir = Directory.systemTemp;
  final tempFile = File(
    '${tempDir.path}/test_video_${DateTime.now().millisecondsSinceEpoch}.mp4',
  );

  // Create a minimal MP4 file with basic header
  final mp4Header = [
    0x00, 0x00, 0x00, 0x20, // Box size (32 bytes)
    0x66, 0x74, 0x79, 0x70, // Box type 'ftyp'
    0x69, 0x73, 0x6F, 0x6D, // Major brand 'isom'
    0x00, 0x00, 0x02, 0x00, // Minor version
    0x69, 0x73, 0x6F, 0x6D, // Compatible brand 'isom'
    0x69, 0x73, 0x6F, 0x32, // Compatible brand 'iso2'
    0x61, 0x76, 0x63, 0x31, // Compatible brand 'avc1'
    0x6D, 0x70, 0x34, 0x31, // Compatible brand 'mp41'
  ];

  await tempFile.writeAsBytes(mp4Header);
  return tempFile.path;
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(const Duration(seconds: 1));
    registerFallbackValue(const VideoCaptureConfig());
  });

  group('[FACE_VERIFICATION] FaceVideoCaptureBloc', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockVideoValidationService mockVideoValidationService;
    late VideoCaptureConfig testConfig;

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockVideoValidationService = MockVideoValidationService();
      testConfig = const VideoCaptureConfig();

      // Setup default mock behaviors
      when(() => mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockFaceDetectionRepository.dispose())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.dispose()).thenAnswer((_) async {});

      // Setup default mock for video validation service
      when(
        () => mockVideoValidationService.calculateCoverageStatsWithQuality(
          any(),
          any(),
        ),
      ).thenReturn(
        const FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 85,
          framesWithValidCoverage: 80,
          averageCoverage: 82.5,
          minimumCoverage: 70,
          maximumCoverage: 95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: [],
          qualityScore: 85,
        ),
      );

      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: mockVideoStorageRepository,
        videoValidationService: mockVideoValidationService,
        config: testConfig,
      );
    });

    tearDown(() {
      bloc.close();
    });

    test('[FACE_VERIFICATION] Action: Initial state verification', () {
      expect(bloc.state, isA<Initial>());
      expect(bloc.state.config, equals(testConfig));
    });

    group('[FACE_VERIFICATION] Action: Camera initialization', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'emits [CameraInitializing, CameraReady] when initialization succeeds',
        build: () => bloc,
        act: (bloc) => bloc.add(const InitializeCamera()),
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
        ],
        verify: (_) {
          verify(() => mockFaceDetectionRepository.initialize()).called(1);
          verify(() => mockVideoStorageRepository.initialize()).called(1);
        },
      );
    });
  });
}
