import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path/path.dart' as path;

// Mock classes
class MockLoggerService extends Mock implements LoggerService {}

class MockDirectory extends Mock implements Directory {}

class MockFile extends Mock implements File {}

// Test implementation of CameraService that allows mocking initialization
class TestCameraService extends CameraService {
  bool _shouldThrowOnInitialize = false;
  bool _shouldThrowOnDirectoryCreate = false;
  late Directory _tempDir;

  void setInitializationException(bool shouldThrow) {
    _shouldThrowOnInitialize = shouldThrow;
  }

  void setDirectoryException(bool shouldThrow) {
    _shouldThrowOnDirectoryCreate = shouldThrow;
  }

  Future<void> setUpTempDirectory() async {
    _tempDir = await Directory.systemTemp.createTemp('camera_service_test_');
  }

  Future<void> cleanUpTempDirectory() async {
    if (_tempDir.existsSync()) {
      await _tempDir.delete(recursive: true);
    }
  }

  String getTestFilePath(String filename) {
    return path.join(_tempDir.path, filename);
  }

  @override
  Future<void> initialize() async {
    if (_shouldThrowOnInitialize) {
      throw Exception('Permission denied');
    }
    return super.initialize();
  }

  @override
  Future<void> startRecording({
    required String filePath,
    VideoCaptureConfig? config,
  }) async {
    if (!isInitialized) {
      throw StateError('Camera service not initialized');
    }

    if (isRecording) {
      throw StateError('Recording already in progress');
    }

    if (_shouldThrowOnDirectoryCreate) {
      throw const FileSystemException('Directory creation failed');
    }

    // Mock the directory creation and file handling for tests
    // Instead of calling super.startRecording which tries to create real directories,
    // we'll simulate the recording state changes
    final file = File(filePath);
    final directory = file.parent;

    // Ensure the test directory exists
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    // Set the recording state without calling the actual camera recording
    _setRecordingState(true, filePath);
  }

  // Helper method to set recording state for testing
  void _setRecordingState(bool recording, String? path) {
    // Access the private fields through reflection or by making them protected
    // For now, we'll use a workaround by calling the parent class methods
    if (recording) {
      // Simulate the state that would be set by the parent class
      _mockCurrentRecordingPath = path;
      _mockIsRecording = true;
    } else {
      _mockCurrentRecordingPath = null;
      _mockIsRecording = false;
    }
  }

  // Mock state variables to track recording state in tests
  String? _mockCurrentRecordingPath;
  bool _mockIsRecording = false;

  @override
  bool get isRecording => _mockIsRecording;

  @override
  String? get currentRecordingPath => _mockCurrentRecordingPath;

  @override
  Map<String, dynamic> getCameraConfig() {
    return {
      'isInitialized': isInitialized,
      'isRecording': _mockIsRecording,
      'currentRecordingPath': _mockCurrentRecordingPath,
      'supportedResolutions': ['480p', '720p', '1080p'],
      'currentLens': 'front',
      'hasFlash': false,
      'hasZoom': true,
    };
  }

  @override
  Future<void> stopRecording() async {
    if (!isInitialized) {
      throw StateError('Camera service not initialized');
    }

    if (!isRecording) {
      throw StateError('No recording in progress');
    }

    // Set the recording state to stopped immediately
    // (like the real implementation)
    _setRecordingState(false, null);

    // Simulate the 500ms grace period after state change
    await Future<void>.delayed(const Duration(milliseconds: 500));
  }
}

void main() {
  group('[FACE_VERIFICATION] CameraService', () {
    late TestCameraService cameraService;

    setUp(() async {
      cameraService = TestCameraService();
      await cameraService.setUpTempDirectory();

      // Register fallback values
      registerFallbackValue(Directory(''));
    });

    tearDown(() async {
      await cameraService.cleanUpTempDirectory();
    });

    group('[FACE_VERIFICATION] Action: Happy Path Flow', () {
      test('complete flow: initialize → startRecording → stopRecording',
          () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        final stopwatch = Stopwatch();

        // 1. Initialize
        await cameraService.initialize();
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // 2. Start recording
        await cameraService.startRecording(filePath: testFilePath);
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));

        // 3. Stop recording and verify 500ms delay
        stopwatch.start();
        await cameraService.stopRecording();
        stopwatch.stop();

        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // Verify the 500ms grace delay
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
      });

      test('verifies internal flags during recording lifecycle', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');

        // Before initialization
        expect(cameraService.isInitialized, isFalse);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // After initialization
        await cameraService.initialize();
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // During recording
        await cameraService.startRecording(filePath: testFilePath);
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));

        // After stopping
        await cameraService.stopRecording();
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Failure Path - Initialize', () {
      test('throws exception when permissions denied', () async {
        cameraService.setInitializationException(true);

        expect(
          () async => cameraService.initialize(),
          throwsA(isA<Exception>()),
        );

        expect(cameraService.isInitialized, isFalse);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });

      test('startRecording throws when not initialized', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');

        expect(
          () async => cameraService.startRecording(
            filePath: testFilePath,
          ),
          throwsA(isA<StateError>()),
        );
      });

      test('stopRecording throws when not initialized', () async {
        expect(
          () async => cameraService.stopRecording(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('[FACE_VERIFICATION] Action: Failure Path - Recording States', () {
      test('startRecording throws when already recording', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        final anotherFilePath =
            cameraService.getTestFilePath('another_video.mp4');

        await cameraService.initialize();
        await cameraService.startRecording(filePath: testFilePath);

        expect(
          () async => cameraService.startRecording(
            filePath: anotherFilePath,
          ),
          throwsA(isA<StateError>()),
        );

        // Verify state hasn't changed
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));
      });

      test('stopRecording throws when not recording', () async {
        await cameraService.initialize();

        expect(
          () async => cameraService.stopRecording(),
          throwsA(isA<StateError>()),
        );

        // Verify state hasn't changed
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Failure Path - Directory Creation', () {
      test('throws FileSystemException when directory creation fails',
          () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        await cameraService.initialize();
        cameraService.setDirectoryException(true);

        expect(
          () async => cameraService.startRecording(
            filePath: testFilePath,
          ),
          throwsA(isA<FileSystemException>()),
        );

        // Verify state remains clean after failure
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Configuration and State Management', () {
      test('startRecording with custom config', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        const config = VideoCaptureConfig(
          recordingDuration: Duration(seconds: 15),
          enableAudio: false,
        );

        await cameraService.initialize();
        await cameraService.startRecording(
          filePath: testFilePath,
          config: config,
        );

        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));

        await cameraService.stopRecording();
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });

      test('getCameraConfig returns correct information', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        await cameraService.initialize();

        var config = cameraService.getCameraConfig();
        expect(config['isInitialized'], isTrue);
        expect(config['isRecording'], isFalse);
        expect(config['currentRecordingPath'], isNull);
        expect(config['supportedResolutions'], isNotEmpty);
        expect(config['currentLens'], equals('front'));

        await cameraService.startRecording(filePath: testFilePath);
        config = cameraService.getCameraConfig();
        expect(config['isRecording'], isTrue);
        expect(config['currentRecordingPath'], equals(testFilePath));

        await cameraService.stopRecording();
        config = cameraService.getCameraConfig();
        expect(config['isRecording'], isFalse);
        expect(config['currentRecordingPath'], isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Grace Period Verification', () {
      test('verifies 500ms grace delay after stopRecording', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        await cameraService.initialize();
        await cameraService.startRecording(filePath: testFilePath);

        final stopwatch = Stopwatch()..start();
        await cameraService.stopRecording();
        stopwatch.stop();

        // Verify the delay is approximately 500ms (allowing for some variance)
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
        expect(
          stopwatch.elapsedMilliseconds,
          lessThan(600),
        ); // Upper bound for reasonable test execution
      });

      test('grace period does not affect state consistency', () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        await cameraService.initialize();
        await cameraService.startRecording(filePath: testFilePath);

        // State should be updated immediately when stopRecording is called
        final stopFuture = cameraService.stopRecording();

        // These should be updated immediately, not after the grace period
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        await stopFuture; // Wait for grace period to complete

        // State should remain consistent
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Error Recovery', () {
      test('service can be reinitialized after initialization failure',
          () async {
        cameraService.setInitializationException(true);

        // First initialization should fail
        expect(
          () async => cameraService.initialize(),
          throwsA(isA<Exception>()),
        );
        expect(cameraService.isInitialized, isFalse);

        // Reset the exception and try again
        cameraService.setInitializationException(false);
        await cameraService.initialize();
        expect(cameraService.isInitialized, isTrue);
      });

      test('recording can be attempted after directory creation failure',
          () async {
        final testFilePath = cameraService.getTestFilePath('video.mp4');
        await cameraService.initialize();
        cameraService.setDirectoryException(true);

        // First recording attempt should fail
        expect(
          () async => cameraService.startRecording(filePath: testFilePath),
          throwsA(isA<FileSystemException>()),
        );
        expect(cameraService.isRecording, isFalse);

        // Reset the exception and try again
        cameraService.setDirectoryException(false);
        await cameraService.startRecording(filePath: testFilePath);
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));
      });
    });
  });
}
