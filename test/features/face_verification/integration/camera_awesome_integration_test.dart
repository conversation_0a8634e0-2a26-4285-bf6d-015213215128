import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for dependencies
class MockFaceDetectionRepository extends Mock
    implements FaceDetectionRepository {}

class MockVideoValidationService extends Mock
    implements VideoValidationService {}

class MockCameraService extends Mock implements CameraService {}

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('[FACE_VERIFICATION] CamerAwesome Integration Tests', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockVideoValidationService mockVideoValidationService;
    late Directory tempDir;

    setUpAll(() {
      registerFallbackValue(const VideoCaptureConfig());
      registerFallbackValue(const Duration(seconds: 1));
      registerFallbackValue(<FaceDetectionResult>[]);
    });

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockVideoValidationService = MockVideoValidationService();
      tempDir = Directory.systemTemp.createTempSync('camera_integration_test_');

      // Default mock behaviors
      when(() => mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockFaceDetectionRepository.dispose())
          .thenAnswer((_) async {});

      // Mock video storage repository with test paths
      final testVideoPath = '${tempDir.path}/test_video.mp4';
      when(() => mockVideoStorageRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.dispose()).thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.currentVideoPath)
          .thenReturn(testVideoPath);

      // Mock video validation service to return good quality stats
      when(
        () => mockVideoValidationService.calculateCoverageStatsWithQuality(
          any(),
          any(),
        ),
      ).thenReturn(
        const FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 85,
          framesWithValidCoverage: 80,
          averageCoverage: 82.5,
          minimumCoverage: 70,
          maximumCoverage: 95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: <FaceDetectionResult>[],
          qualityScore: 85, // Above 70% threshold
        ),
      );

      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: mockVideoStorageRepository,
        videoValidationService: mockVideoValidationService,
      );
    });

    tearDown(() {
      bloc.close();
      tempDir.deleteSync(recursive: true);
    });

    group('[FACE_VERIFICATION] Real CamerAwesome File Creation', () {
      test('should investigate why CamerAwesome is not creating video files',
          () async {
        // This test verifies that the video storage repository provides
        // a valid path for video recording

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act - Initialize camera
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Verify camera initialization was attempted
        final hasInitializing =
            states.any((state) => state is CameraInitializing);
        expect(hasInitializing, isTrue);

        // Get the expected video path from the repository
        final expectedPath = mockVideoStorageRepository.currentVideoPath;
        expect(expectedPath, isNotNull);
        expect(expectedPath, isNotEmpty);

        // Verify the path is in the expected format
        expect(expectedPath, contains('test_video.mp4'));
        expect(expectedPath, contains(tempDir.path));

        await subscription.cancel();
      });

      test('should test CamerAwesome configuration and initialization',
          () async {
        // This test verifies the video storage repository configuration

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Initialize camera and examine the configuration
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Verify initialization was attempted
        final hasInitializing =
            states.any((state) => state is CameraInitializing);
        expect(hasInitializing, isTrue);

        // Check the video storage repository configuration
        final videoPath = mockVideoStorageRepository.currentVideoPath;
        expect(videoPath, isNotNull);
        expect(videoPath, contains('.mp4'));

        await subscription.cancel();
      });

      test('should test manual video file creation to verify path validity',
          () async {
        // This test verifies that the video path can be used for file creation

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Initialize camera
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        final hasInitializing =
            states.any((state) => state is CameraInitializing);
        expect(hasInitializing, isTrue);

        // Get the expected video path
        final expectedPath = mockVideoStorageRepository.currentVideoPath;
        expect(expectedPath, isNotNull);

        // Verify the path structure is valid
        final videoFile = File(expectedPath!);
        expect(videoFile.parent.path, isNotEmpty);
        expect(expectedPath.endsWith('.mp4'), isTrue);

        await subscription.cancel();
      });
    });

    group('[FACE_VERIFICATION] CamerAwesome Integration Analysis', () {
      test('should analyze the video recording workflow timing', () async {
        // This test analyzes the timing between recording stop and
        // VideoRecordingCompleted event to understand the timeout issue

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);
        final stopwatch = Stopwatch();

        // Initialize and start recording
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Stop recording and start timing
        stopwatch.start();
        bloc.add(const StopRecording());

        // Wait for processing state
        await Future<void>.delayed(const Duration(milliseconds: 100));
        expect(states.any((state) => state is Processing), isTrue);

        // Wait for timeout (1 second) and measure
        await Future<void>.delayed(const Duration(milliseconds: 1200));
        stopwatch.stop();

        print('[CAMERA_INTEGRATION] Timing Analysis:');
        print('  Time elapsed: ${stopwatch.elapsedMilliseconds}ms');
        print('  Processing timeout: 1000ms');
        print('  VideoRecordingCompleted received: '
            '${states.any((state) => state is Success || state is Failure)}');

        // Check if timeout was triggered
        final hasError = states.any((state) => state is Error);
        if (hasError) {
          final errorState = states.whereType<Error>().first;
          print('  Timeout error: ${errorState.error}');
        }

        await subscription.cancel();
      });
    });
  });
}
