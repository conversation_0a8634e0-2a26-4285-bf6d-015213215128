import 'package:bloomg_flutter/core/storage/hive_service.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockHiveService extends Mock implements HiveService {}

class MockFirebaseFirestore extends Mock implements FirebaseFirestore {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockBox<T> extends Mock implements Box<T> {}

class MockUser extends Mock implements User {}

class MockCollectionReference extends Mock
    implements CollectionReference<Map<String, dynamic>> {}

class MockDocumentReference extends Mock
    implements DocumentReference<Map<String, dynamic>> {}

class MockDocumentSnapshot extends Mock
    implements DocumentSnapshot<Map<String, dynamic>> {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(const AsvsUserProfile());
    registerFallbackValue(<String, dynamic>{});
  });

  group('[ONBOARDING] AsvsProfileRepository', () {
    late FirebaseAsvsProfileRepository repository;
    late MockHiveService mockHiveService;
    late MockFirebaseFirestore mockFirestore;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockBox<AsvsUserProfile> mockAsvsProfileBox;
    late MockUser mockUser;
    late MockCollectionReference mockCollection;
    late MockDocumentReference mockDocument;
    late MockDocumentSnapshot mockSnapshot;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
      isOnboardingComplete: true,
    );

    setUp(() {
      mockHiveService = MockHiveService();
      mockFirestore = MockFirebaseFirestore();
      mockFirebaseAuth = MockFirebaseAuth();
      mockAsvsProfileBox = MockBox<AsvsUserProfile>();
      mockUser = MockUser();
      mockCollection = MockCollectionReference();
      mockDocument = MockDocumentReference();
      mockSnapshot = MockDocumentSnapshot();

      repository = FirebaseAsvsProfileRepository(
        hiveService: mockHiveService,
        firestore: mockFirestore,
        firebaseAuth: mockFirebaseAuth,
      );

      // Default mock behaviors
      when(() => mockHiveService.initialize()).thenAnswer((_) async {});
      when(() => mockHiveService.asvsProfileBox).thenReturn(mockAsvsProfileBox);
      when(() => mockAsvsProfileBox.get(any<String>())).thenReturn(null);
      when(() => mockAsvsProfileBox.put(any<String>(), any<AsvsUserProfile>()))
          .thenAnswer((_) async {});
      when(() => mockAsvsProfileBox.delete(any<String>()))
          .thenAnswer((_) async {});
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(() => mockUser.uid).thenReturn('test-user-id');

      // Firebase Firestore mocks
      when(() => mockFirestore.collection(any())).thenReturn(mockCollection);
      when(() => mockCollection.doc(any())).thenReturn(mockDocument);
      when(() => mockDocument.set(any<Map<String, dynamic>>()))
          .thenAnswer((_) async {});
      when(() => mockDocument.get()).thenAnswer((_) async => mockSnapshot);
      when(() => mockDocument.delete()).thenAnswer((_) async {});
      when(() => mockSnapshot.exists).thenReturn(false);
    });

    group('[ONBOARDING] Action: Initialize repository', () {
      test('[ONBOARDING] Action: Initializes successfully', () async {
        // Act
        await repository.initialize();

        // Assert
        verify(() => mockHiveService.initialize()).called(1);
      });

      test('[ONBOARDING] Action: Handles initialization error', () async {
        // Arrange
        when(() => mockHiveService.initialize())
            .thenThrow(Exception('Hive initialization failed'));

        // Act & Assert
        expect(
          () => repository.initialize(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('[ONBOARDING] Action: Save profile', () {
      test('[ONBOARDING] Action: Saves profile successfully', () async {
        // Act
        await repository.saveProfile(testProfile);

        // Assert
        verify(
          () => mockAsvsProfileBox.put(
            'current_profile',
            testProfile,
          ),
        ).called(1);
      });

      test('[ONBOARDING] Action: Handles save error', () async {
        // Arrange
        when(
          () => mockAsvsProfileBox.put(any<String>(), any<AsvsUserProfile>()),
        ).thenThrow(Exception('Save failed'));

        // Act & Assert
        expect(
          () => repository.saveProfile(testProfile),
          throwsA(isA<Exception>()),
        );
      });

      test('[ONBOARDING] Action: Saves profile with correct data structure',
          () async {
        // Arrange
        AsvsUserProfile? capturedProfile;
        when(
          () => mockAsvsProfileBox.put(any<String>(), any<AsvsUserProfile>()),
        ).thenAnswer((invocation) async {
          capturedProfile =
              invocation.positionalArguments[1] as AsvsUserProfile;
        });

        // Act
        await repository.saveProfile(testProfile);

        // Assert
        expect(capturedProfile, isNotNull);
        expect(capturedProfile!.firstName, equals('John'));
        expect(capturedProfile!.lastName, equals('Doe'));
        expect(capturedProfile!.gender, equals(Gender.male));
        expect(capturedProfile!.unitPreference, equals(UnitPreference.metric));
        expect(capturedProfile!.smokingStatus, equals(SmokingStatus.never));
        expect(capturedProfile!.heightCm, equals(180));
        expect(capturedProfile!.weightKg, equals(75));
        expect(capturedProfile!.isOnboardingComplete, equals(true));
      });
    });

    group('[ONBOARDING] Action: Load profile', () {
      test('[ONBOARDING] Action: Loads profile successfully', () async {
        // Arrange
        when(() => mockAsvsProfileBox.get('current_profile'))
            .thenReturn(testProfile);

        // Act
        final result = await repository.loadProfile();

        // Assert
        expect(result, isNotNull);
        expect(result!.firstName, equals('John'));
        expect(result.lastName, equals('Doe'));
        expect(result.gender, equals(Gender.male));
        expect(result.unitPreference, equals(UnitPreference.metric));
        expect(result.smokingStatus, equals(SmokingStatus.never));
        expect(result.heightCm, equals(180));
        expect(result.weightKg, equals(75));
        expect(result.isOnboardingComplete, equals(true));
        verify(() => mockAsvsProfileBox.get('current_profile')).called(1);
      });

      test('[ONBOARDING] Action: Returns null when no profile exists',
          () async {
        // Arrange
        when(() => mockAsvsProfileBox.get('current_profile')).thenReturn(null);

        // Act
        final result = await repository.loadProfile();

        // Assert
        expect(result, isNull);
        verify(() => mockAsvsProfileBox.get('current_profile')).called(1);
      });

      test('[ONBOARDING] Action: Handles corrupted profile data gracefully',
          () async {
        // This test is not applicable since we're using typed Hive boxes
        // that automatically handle serialization/deserialization
        // Arrange
        when(() => mockAsvsProfileBox.get('current_profile')).thenReturn(null);

        // Act
        final result = await repository.loadProfile();

        // Assert
        expect(result, isNull);
      });

      test('[ONBOARDING] Action: Handles load error', () async {
        // Arrange
        when(() => mockAsvsProfileBox.get('current_profile'))
            .thenThrow(Exception('Load failed'));

        // Act
        final result = await repository.loadProfile();

        // Assert - Repository should handle errors gracefully and return null
        expect(result, isNull);
      });

      test('[ONBOARDING] Action: Handles enum parsing correctly', () async {
        // Arrange
        const differentProfile = AsvsUserProfile(
          firstName: 'Jane',
          lastName: 'Smith',
          gender: Gender.female,
          unitPreference: UnitPreference.imperial,
          smokingStatus: SmokingStatus.current,
        );

        when(() => mockAsvsProfileBox.get('current_profile'))
            .thenReturn(differentProfile);

        // Act
        final result = await repository.loadProfile();

        // Assert
        expect(result, isNotNull);
        expect(result!.gender, equals(Gender.female));
        expect(result.unitPreference, equals(UnitPreference.imperial));
        expect(result.smokingStatus, equals(SmokingStatus.current));
      });
    });

    group('[ONBOARDING] Action: Delete profile', () {
      test('[ONBOARDING] Action: Deletes profile successfully', () async {
        // Act
        await repository.deleteProfile();

        // Assert
        verify(() => mockAsvsProfileBox.delete('current_profile')).called(1);
      });

      test('[ONBOARDING] Action: Handles delete error', () async {
        // Arrange
        when(() => mockAsvsProfileBox.delete(any<String>()))
            .thenThrow(Exception('Delete failed'));

        // Act & Assert
        expect(
          () => repository.deleteProfile(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('[ONBOARDING] Action: Has profile check', () {
      test('[ONBOARDING] Action: Returns true when profile exists', () async {
        // Arrange
        when(() => mockAsvsProfileBox.get('current_profile'))
            .thenReturn(testProfile);

        // Act
        final result = await repository.hasProfile();

        // Assert
        expect(result, isTrue);
      });

      test('[ONBOARDING] Action: Returns false when profile does not exist',
          () async {
        // Arrange
        when(() => mockAsvsProfileBox.get('current_profile')).thenReturn(null);

        // Act
        final result = await repository.hasProfile();

        // Assert
        expect(result, isFalse);
      });
    });

    group('[ONBOARDING] Action: Repository lifecycle', () {
      test('[ONBOARDING] Action: Initializes successfully', () async {
        // Act
        await repository.initialize();

        // Assert
        verify(() => mockHiveService.initialize()).called(1);
      });

      test('[ONBOARDING] Action: Disposes successfully', () async {
        // Act
        await repository.dispose();

        // Assert - No specific verification needed as dispose is a cleanup
        // method
        expect(true, isTrue);
      });
    });
  });
}
