import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/main.dart' as app;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mocktail/mocktail.dart';

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Face Verification Flow Integration Tests', () {
    late MockVideoStorageRepository mockVideoStorageRepository;

    setUpAll(() async {
      // Initialize dependency injection
      await Injection.initialize();

      // Setup mocks
      mockVideoStorageRepository = MockVideoStorageRepository();

      // Register mock fallbacks
      registerFallbackValue(
        FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        ),
      );
    });

    setUp(() {
      // Reset mocks before each test
      reset(mockVideoStorageRepository);
    });

    testWidgets('Complete face verification flow with video recording',
        (tester) async {
      // Mock successful video recording operations
      when(() => mockVideoStorageRepository.startRecording())
          .thenAnswer((_) async {});

      when(() => mockVideoStorageRepository.stopRecording())
          .thenAnswer((_) async {});

      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to face verification from home
      final faceVerificationButton = find.text('Face Verification');
      expect(faceVerificationButton, findsOneWidget);
      await tester.tap(faceVerificationButton);
      await tester.pumpAndSettle();

      // Verify we're on the face verification screen
      expect(find.text('Face Verification'), findsOneWidget);

      // Wait for face detection and tap on screen to start recording
      await tester.pump(const Duration(seconds: 1));
      await tester.tapAt(const Offset(200, 400)); // Tap center of screen
      await tester.pumpAndSettle();

      // Verify recording state
      expect(find.text('Stop Recording'), findsOneWidget);

      // Wait for minimum recording time (simulate 2 seconds)
      await tester.pump(const Duration(seconds: 2));

      // Stop recording
      final stopButton = find.text('Stop Recording');
      await tester.tap(stopButton);
      await tester.pumpAndSettle();

      // Verify processing state
      expect(find.text('Processing...'), findsOneWidget);

      // Wait for processing to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify results screen
      expect(find.text('Verification Results'), findsOneWidget);
      expect(find.text('Face detected successfully'), findsOneWidget);

      // Navigate to video gallery
      final viewGalleryButton = find.text('View Gallery');
      expect(viewGalleryButton, findsOneWidget);
      await tester.tap(viewGalleryButton);
      await tester.pumpAndSettle();

      // Verify we're in the video gallery
      expect(find.text('Video Gallery'), findsOneWidget);

      // Verify video recording operations were called
      verify(() => mockVideoStorageRepository.startRecording()).called(1);
      verify(() => mockVideoStorageRepository.stopRecording()).called(1);
    });

    testWidgets('Video recording error handling', (tester) async {
      // Mock video recording failure
      when(() => mockVideoStorageRepository.startRecording())
          .thenThrow(Exception('Recording failed'));

      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to face verification
      final faceVerificationButton = find.text('Face Verification');
      await tester.tap(faceVerificationButton);
      await tester.pumpAndSettle();

      // Start and stop recording
      await tester.tap(find.text('Start Recording'));
      await tester.pumpAndSettle();
      await tester.pump(const Duration(seconds: 2));
      await tester.tap(find.text('Stop Recording'));
      await tester.pumpAndSettle();

      // Wait for processing
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify error handling
      expect(find.text('Error'), findsOneWidget);
      expect(find.text('Failed to process video'), findsOneWidget);

      // Verify retry option
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('Video recording with face detection feedback', (tester) async {
      // Mock successful operations
      when(() => mockVideoStorageRepository.startRecording())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.stopRecording())
          .thenAnswer((_) async {});

      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to face verification
      final faceVerificationButton = find.text('Face Verification');
      await tester.tap(faceVerificationButton);
      await tester.pumpAndSettle();

      // Start recording by tapping on screen
      await tester.pump(const Duration(seconds: 1));
      await tester.tapAt(const Offset(200, 400)); // Tap center of screen
      await tester.pumpAndSettle();

      // Verify real-time feedback elements are present
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Recording...'), findsOneWidget);

      // Simulate face detection feedback
      await tester.pump(const Duration(milliseconds: 500));

      // Stop recording after sufficient time
      await tester.pump(const Duration(seconds: 2));
      await tester.tap(find.text('Stop Recording'));
      await tester.pumpAndSettle();

      // Verify processing and results
      await tester.pumpAndSettle(const Duration(seconds: 3));
      expect(find.text('Verification Results'), findsOneWidget);
    });
  });
}
