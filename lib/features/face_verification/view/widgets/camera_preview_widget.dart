import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:path_provider/path_provider.dart';

/// {@template camera_preview_widget}
/// Widget that displays the camera preview with face detection overlay.
///
/// Shows a full-screen camera preview with a face guide overlay
/// and real-time face detection feedback.
/// {@endtemplate}
class CameraPreviewWidget extends StatefulWidget {
  /// {@macro camera_preview_widget}
  const CameraPreviewWidget({
    super.key,
    this.onOverlayTap,
  });

  /// Callback when the overlay is tapped
  final VoidCallback? onOverlayTap;

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget> {
  final LoggerService _logger = LoggerService();
  FaceDetectionService? _faceDetectionService;
  Timer? _faceDetectionTimer;
  bool _isProcessingFrame = false;
  int _frameCount = 0; // For frame throttling

  // CamerAwesome state for programmatic recording control
  CameraState? _cameraState;

  // Recording state tracking
  bool _isRecordingActive = false;
  String? _expectedVideoPath;

  @override
  void initState() {
    super.initState();
    _initializeFaceDetectionService();
  }

  @override
  void dispose() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposing',
        'Recording active: $_isRecordingActive',
      ),
    );

    // CRITICAL FIX: Prevent disposal during active recording
    if (_isRecordingActive) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Widget disposal blocked - recording still active',
          'Expected path: $_expectedVideoPath',
        ),
      );

      // Force stop recording before disposal
      _forceStopRecordingOnDisposal();
    }

    // Cancel timer first to stop processing
    _faceDetectionTimer?.cancel();
    _faceDetectionTimer = null;

    // Dispose face detection service
    _faceDetectionService?.dispose();
    _faceDetectionService = null;

    // Clear processing flag
    _isProcessingFrame = false;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposed successfully',
      ),
    );

    super.dispose();
  }

  /// Forces recording to stop during widget disposal to prevent file creation interruption
  void _forceStopRecordingOnDisposal() {
    _logger.warning(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Force stopping recording due to widget disposal',
        'This may indicate a lifecycle management issue',
      ),
    );

    if (_cameraState != null) {
      _cameraState!.when(
        onVideoRecordingMode: (recordingState) {
          try {
            recordingState.stopRecording();
            _isRecordingActive = false;

            _logger.info(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'Recording force-stopped during disposal',
                'Expected path: $_expectedVideoPath',
              ),
            );
          } catch (error) {
            _logger.error(
              LoggingConstants.formatError(
                LoggingConstants.faceVerificationModule,
                LoggingConstants.recoverableError,
                'Failed to force stop recording during disposal: $error',
              ),
            );
          }
        },
        onVideoMode: (videoState) => null,
        onPhotoMode: (photoState) => null,
        onPreparingCamera: (preparingState) => null,
      );
    }
  }

  /// Initializes the face detection service (main thread)
  Future<void> _initializeFaceDetectionService() async {
    try {
      _faceDetectionService = FaceDetectionService();
      await _faceDetectionService!.initialize();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialized',
          'Main thread processing with frame throttling for optimal '
              'performance',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow; // Re-throw to prevent silent failures
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FaceVideoCaptureBloc, FaceVideoCaptureState>(
      listener: (context, state) {
        _handleBlocStateChange(state);
      },
      child: BlocBuilder<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        builder: (context, state) {
          return Stack(
            children: [
              // Camera preview background
              _buildCameraPreview(context, state),

              // Face guide overlay
              FaceGuideOverlay(
                currentDetection: state.currentDetection,
                isRecording: state is Recording,
                onTap: widget.onOverlayTap,
              ),
            ],
          );
        },
      ),
    );
  }

  /// Builds the camera preview with CamerAwesome integration
  Widget _buildCameraPreview(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    // Handle different states
    if (state is CameraInitializing) {
      return _buildLoadingState();
    }

    if (state is Error) {
      return _buildErrorState(state.errorMessage ?? 'Camera error occurred');
    }

    // Build CamerAwesome widget for camera ready and recording states
    return _buildCameraAwesome(context, state);
  }

  /// Builds the loading state during camera initialization
  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing Camera...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the error state when camera fails
  Widget _buildErrorState(String errorMessage) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<FaceVideoCaptureBloc>()
                    .add(const InitializeCamera());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Handles BLoC state changes for recording control
  void _handleBlocStateChange(FaceVideoCaptureState state) {
    if (state is Recording && !_isRecordingActive) {
      // CRITICAL FIX: Get the expected video path from the repository
      // BEFORE starting recording so videoPathBuilder can use it
      _updateExpectedVideoPathFromRepository();
      _startCameraRecording();
    } else if (state is Processing && _isRecordingActive) {
      // Capture the expected video path from the Processing state
      final processingState = state;
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Expected video path CAPTURED from Processing state',
          'Previous: $_expectedVideoPath, '
              'New: ${processingState.expectedVideoPath}',
        ),
      );
      _expectedVideoPath = processingState.expectedVideoPath;
      _stopCameraRecording();
    }
  }

  /// Starts CamerAwesome video recording
  void _startCameraRecording() {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Attempting to start CamerAwesome recording',
        'Expected path: $_expectedVideoPath, Camera state available: ${_cameraState != null}',
      ),
    );

    if (_cameraState != null) {
      _cameraState!.when(
        onVideoMode: (videoState) {
          try {
            videoState.startRecording();
            _isRecordingActive = true;
            _logger.info(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'CamerAwesome recording started successfully',
                'Expected path: $_expectedVideoPath',
              ),
            );
          } catch (error) {
            _logger.error(
              LoggingConstants.formatError(
                LoggingConstants.faceVerificationModule,
                LoggingConstants.criticalError,
                'Failed to start CamerAwesome recording: $error',
              ),
            );
            // Notify BLoC of recording failure
            if (mounted) {
              context.read<FaceVideoCaptureBloc>().add(const ResetCapture());
            }
          }
        },
        onVideoRecordingMode: (recordingState) {
          // Already recording
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'CamerAwesome already recording',
            ),
          );
          _isRecordingActive = true;
        },
        onPhotoMode: (photoState) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Cannot start recording in photo mode',
            ),
          );
          // Notify BLoC that recording cannot start in photo mode
          if (mounted) {
            context.read<FaceVideoCaptureBloc>().add(const ResetCapture());
          }
        },
        onPreparingCamera: (preparingState) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Cannot start recording while camera is preparing',
            ),
          );
          // Retry after a short delay
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && _isRecordingActive) {
              _startCameraRecording();
            }
          });
        },
      );
    } else {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Cannot start recording - camera state is null',
        ),
      );
      // Notify BLoC of recording failure
      if (mounted) {
        context.read<FaceVideoCaptureBloc>().add(const ResetCapture());
      }
    }
  }

  /// Updates the expected video path from the repository before recording
  void _updateExpectedVideoPathFromRepository() {
    try {
      // Get the current video path from the repository
      final videoStorageRepository = context.read<VideoStorageRepository>();
      final currentPath = videoStorageRepository.currentVideoPath;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Expected video path UPDATED from repository',
          'Previous: $_expectedVideoPath, New: $currentPath',
        ),
      );

      _expectedVideoPath = currentPath;
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Failed to get expected video path from repository',
          'Error: $error, will use fallback path in videoPathBuilder',
        ),
      );
    }
  }

  /// Stops CamerAwesome video recording
  void _stopCameraRecording() {
    if (_cameraState != null) {
      _cameraState!.when(
        onVideoRecordingMode: (recordingState) {
          recordingState.stopRecording();
          _isRecordingActive = false;
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'CamerAwesome recording stopped',
              'Expected path: $_expectedVideoPath',
            ),
          );

          // CRITICAL FIX: Now that we use custom videoPathBuilder,
          // CamerAwesome will save the file at the expected path and
          // trigger onMediaTap callback automatically. No need for
          // immediate dispatch - let onMediaTap handle it properly.
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Waiting for CamerAwesome onMediaTap callback',
              'File should be saved at: $_expectedVideoPath',
            ),
          );
        },
        onVideoMode: (videoState) {
          // Not recording
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'CamerAwesome not recording',
            ),
          );
        },
        onPhotoMode: (photoState) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Cannot stop recording in photo mode',
            ),
          );
        },
        onPreparingCamera: (preparingState) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Cannot stop recording while camera is preparing',
            ),
          );
        },
      );
    }
  }

  /// Builds the CamerAwesome widget with face detection integration
  Widget _buildCameraAwesome(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    return CameraAwesomeBuilder.awesome(
      // CRITICAL FIX: Use video mode for actual video recording
      // This enables proper MP4 video file creation
      saveConfig: SaveConfig.photoAndVideo(
        initialCaptureMode: CaptureMode.video,
        // CRITICAL FIX: Use custom videoPathBuilder to ensure CamerAwesome
        // saves the file at the exact path expected by the repository
        videoPathBuilder: (sensors) async {
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'videoPathBuilder CALLED by CamerAwesome',
              'Sensors count: ${sensors.length}, '
                  'Expected path: $_expectedVideoPath',
            ),
          );

          try {
            // Use the expected video path from the repository if available
            if (_expectedVideoPath != null) {
              _logger.info(
                LoggingConstants.formatMessage(
                  LoggingConstants.faceVerificationModule,
                  'videoPathBuilder returning EXPECTED path',
                  'Path: $_expectedVideoPath',
                ),
              );

              // Ensure the directory exists before returning the path
              final file = File(_expectedVideoPath!);
              final directory = file.parent;

              // CRITICAL FIX: Add error handling for directory creation
              try {
                if (!await directory.exists()) {
                  await directory.create(recursive: true);
                  _logger.debug(
                    LoggingConstants.formatMessage(
                      LoggingConstants.faceVerificationModule,
                      'Created directory for expected video path',
                      'Directory: ${directory.path}',
                    ),
                  );
                }
              } catch (dirError) {
                _logger.error(
                  LoggingConstants.formatError(
                    LoggingConstants.faceVerificationModule,
                    LoggingConstants.recoverableError,
                    'Failed to create directory: $dirError',
                  ),
                );
                // Continue with fallback path generation
                return _generateFallbackPath(sensors);
              }

              final captureRequest =
                  SingleCaptureRequest(_expectedVideoPath, sensors.first);
              _logger.info(
                LoggingConstants.formatMessage(
                  LoggingConstants.faceVerificationModule,
                  'videoPathBuilder returning SingleCaptureRequest',
                  'File path: ${captureRequest.file?.path}, '
                      'Sensor: ${captureRequest.sensor}',
                ),
              );
              return captureRequest;
            }

            // Fallback to default behavior if no expected path is available
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'videoPathBuilder: No expected video path available, '
                'generating fallback',
              ),
            );

            return _generateFallbackPath(sensors);
          } catch (error, stackTrace) {
            _logger.error(
              LoggingConstants.formatError(
                LoggingConstants.faceVerificationModule,
                LoggingConstants.criticalError,
                'videoPathBuilder failed: $error',
              ),
              error,
              stackTrace,
            );

            // Emergency fallback - use temporary directory
            return _generateEmergencyFallbackPath(sensors);
          }
        },
      ),
      sensorConfig: SensorConfig.single(
        sensor: Sensor.position(SensorPosition.front),
        aspectRatio: CameraAspectRatios.ratio_16_9,
      ),
      onMediaTap: _handleVideoCapture,
      onImageForAnalysis: (analysisImage) async {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame received for analysis',
            'Size: ${analysisImage.width}x${analysisImage.height}, '
                'Format: ${analysisImage.runtimeType}',
          ),
        );
        _processFrameForFaceDetection(analysisImage);
      },
      imageAnalysisConfig: AnalysisConfig(
        androidOptions: const AndroidAnalysisOptions.nv21(
          width: 480,
          // Higher resolution for better face detection
          //Autostart is always true no need to add it here
        ),
        maxFramesPerSecond: 10, // Optimized FPS for face detection
      ),
      theme: AwesomeTheme(
        bottomActionsBackgroundColor: Colors.transparent,
        buttonTheme: AwesomeButtonTheme(
          backgroundColor: Colors.transparent,
          iconSize: 0, // Hide default buttons
        ),
      ),
    );
  }

  /// Generates a fallback video path when expected path is not available
  Future<SingleCaptureRequest> _generateFallbackPath(
    List<Sensor> sensors,
  ) async {
    try {
      // Generate a fallback path similar to the repository's logic
      final directory = await getApplicationDocumentsDirectory();
      final videoDir = Directory('${directory.path}/face_verification_videos');
      if (!await videoDir.exists()) {
        await videoDir.create(recursive: true);
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Created fallback video directory',
            'Directory: ${videoDir.path}',
          ),
        );
      }
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fallbackPath = '${videoDir.path}/face_verification_$timestamp.mp4';

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'videoPathBuilder returning FALLBACK path',
          'Path: $fallbackPath',
        ),
      );

      final fallbackRequest = SingleCaptureRequest(fallbackPath, sensors.first);
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'videoPathBuilder returning fallback SingleCaptureRequest',
          'File path: ${fallbackRequest.file?.path}, '
              'Sensor: ${fallbackRequest.sensor}',
        ),
      );

      return fallbackRequest;
    } catch (error) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Fallback path generation failed: $error',
        ),
      );
      return _generateEmergencyFallbackPath(sensors);
    }
  }

  /// Generates an emergency fallback path using temporary directory
  SingleCaptureRequest _generateEmergencyFallbackPath(List<Sensor> sensors) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final emergencyPath = '/tmp/face_verification_emergency_$timestamp.mp4';

    _logger.warning(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Using EMERGENCY fallback path',
        'Path: $emergencyPath',
      ),
    );

    return SingleCaptureRequest(emergencyPath, sensors.first);
  }

  /// Handles video capture completion from CamerAwesome
  /// This is called when user manually taps capture or when recording completes
  void _handleVideoCapture(MediaCapture mediaCapture) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'onMediaTap callback TRIGGERED by CamerAwesome',
        'Media type: ${mediaCapture.isPicture ? "photo" : "video"}, '
            'Capture request type: ${mediaCapture.captureRequest.runtimeType}',
      ),
    );

    final videoPath = mediaCapture.captureRequest.when(
      single: (single) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'onMediaTap: Single capture request detected',
            'File path: ${single.file?.path}',
          ),
        );
        return single.file?.path;
      },
      multiple: (multiple) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'onMediaTap: Multiple capture request detected (unexpected)',
            'Multiple capture not supported for video recording',
          ),
        );
        return null;
      },
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'onMediaTap: Extracted video path from capture request',
        'Video path: $videoPath, Expected path: $_expectedVideoPath',
      ),
    );

    if (videoPath != null) {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video recording completed by CamerAwesome onMediaTap',
          'Actual path: $videoPath, Expected: $_expectedVideoPath',
        ),
      );

      // CRITICAL FIX: Verify path synchronization and handle mismatches
      if (videoPath == _expectedVideoPath) {
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Path synchronization successful',
            'CamerAwesome saved file at expected location',
          ),
        );
      } else {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Path synchronization mismatch detected',
            'Expected: $_expectedVideoPath, Actual: $videoPath',
          ),
        );

        // ALTERNATIVE FILE HANDLING: If paths don't match, try to move the file
        // to the expected location to maintain consistency with the repository
        if (_expectedVideoPath != null) {
          _handlePathMismatch(videoPath, _expectedVideoPath!);
          return; // Exit early, _handlePathMismatch will dispatch the event
        }
      }

      // Send the actual file path to the BLoC
      if (mounted) {
        context.read<FaceVideoCaptureBloc>().add(
              VideoRecordingCompleted(videoPath: videoPath),
            );
      }
    } else {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video recording completed but no file path provided',
        ),
      );

      // Send error to BLoC
      if (mounted) {
        context.read<FaceVideoCaptureBloc>().add(
              const ResetCapture(),
            );
      }
    }
  }

  /// Handles path mismatch by attempting to move the file to expected location
  Future<void> _handlePathMismatch(
    String actualPath,
    String expectedPath,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Attempting to resolve path mismatch',
        'Moving file from $actualPath to $expectedPath',
      ),
    );

    try {
      final actualFile = File(actualPath);
      final expectedFile = File(expectedPath);

      // Verify the actual file exists
      if (!await actualFile.exists()) {
        throw Exception('Source file does not exist: $actualPath');
      }

      // Ensure the target directory exists
      final targetDirectory = expectedFile.parent;
      if (!await targetDirectory.exists()) {
        await targetDirectory.create(recursive: true);
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Created target directory for file move',
            'Directory: ${targetDirectory.path}',
          ),
        );
      }

      // Move the file to the expected location
      await actualFile.rename(expectedPath);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'File successfully moved to expected location',
          'New path: $expectedPath',
        ),
      );

      // Send the expected path to the BLoC (the path the repository expects)
      if (mounted) {
        context.read<FaceVideoCaptureBloc>().add(
              VideoRecordingCompleted(videoPath: expectedPath),
            );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Failed to move file to expected location: $error',
        ),
        error,
        stackTrace,
      );

      // Fallback: Use the actual path where CamerAwesome saved the file
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Using actual file path as fallback',
          'Path: $actualPath',
        ),
      );

      if (mounted) {
        context.read<FaceVideoCaptureBloc>().add(
              VideoRecordingCompleted(videoPath: actualPath),
            );
      }
    }
  }

  /// Processes camera frames for real-time face detection on main thread
  void _processFrameForFaceDetection(AnalysisImage analysisImage) {
    // CRITICAL FIX: Process every 2nd frame instead of every 3rd for better
    // consistency with reduced buffer size
    _frameCount++;
    if (_frameCount % 2 != 0) {
      return;
    }

    // Skip if already processing a frame, service is null, or widget disposed
    if (_isProcessingFrame || _faceDetectionService == null || !mounted) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Frame processing skipped',
          'Processing: $_isProcessingFrame, '
              'Service: ${_faceDetectionService != null}, Mounted: $mounted',
        ),
      );
      return;
    }

    _isProcessingFrame = true;
    final frameStartTime = DateTime.now();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Frame processing started',
        'Size: ${analysisImage.width}x${analysisImage.height}, '
            'Frame: $_frameCount',
      ),
    );

    // Convert AnalysisImage to InputImage for ML Kit processing
    final conversionStartTime = DateTime.now();
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Starting InputImage conversion',
        'Frame: $_frameCount, '
            'AnalysisImage type: ${analysisImage.runtimeType}, '
            'Size: ${analysisImage.width}x${analysisImage.height}',
      ),
    );

    final inputImageFuture = _convertAnalysisImageToInputImage(analysisImage);

    inputImageFuture.then((inputImage) {
      final conversionTime =
          DateTime.now().difference(conversionStartTime).inMilliseconds;

      if (inputImage == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Failed to convert AnalysisImage to InputImage',
            'Frame processing aborted - conversion failed, '
                'Frame: $_frameCount, Type: ${analysisImage.runtimeType}, '
                'Size: ${analysisImage.width}x${analysisImage.height}, '
                'Conversion time: ${conversionTime}ms',
          ),
        );
        _isProcessingFrame = false;

        // Track conversion failure for diagnostics
        _faceDetectionService?.trackConversionFailure();

        // Send a failed detection result to maintain frame buffer consistency
        final failedResult = FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: DateTime.now(),
        );

        if (mounted) {
          try {
            // Notify BLoC about conversion failure for tracking
            context
                .read<FaceVideoCaptureBloc>()
                .add(ProcessFrame(failedResult));
          } catch (error) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'Failed to send conversion failure to BLoC',
                'Error: $error',
              ),
            );
          }
        }
        return;
      }

      // Log successful conversion with timing
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'InputImage conversion successful',
          'Frame: $_frameCount, Conversion time: ${conversionTime}ms, '
              'Size: ${inputImage.metadata?.size}, '
              'Format: ${inputImage.metadata?.format}',
        ),
      );

      // Track successful conversion for diagnostics
      _faceDetectionService?.trackConversionSuccess();

      // Process frame using main thread service with increased timeout
      // CRITICAL FIX: Increase timeout from 500ms to 750ms for slower devices
      final processingFuture =
          _faceDetectionService!.processImage(inputImage).timeout(
        const Duration(
          milliseconds: 750,
        ), // Increased timeout for better Android stability
        onTimeout: () {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Frame processing timeout on main thread',
              'Processing took longer than 750ms - device may be slow',
            ),
          );
          return null;
        },
      );

      processingFuture.then((result) {
        final processingTime = DateTime.now().difference(frameStartTime);

        if (result != null && mounted) {
          try {
            // Send detection result to BLoC
            context.read<FaceVideoCaptureBloc>().add(ProcessFrame(result));

            _logger.debug(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'ProcessFrame event sent to BLoC',
                'Faces: ${result.faceCount}, Coverage: '
                    '${result.coveragePercentage.toStringAsFixed(1)}%, '
                    'Time: ${processingTime.inMilliseconds}ms',
              ),
            );
          } catch (error) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'BLoC access failed during frame processing',
                'Error: $error',
              ),
            );
          }
        } else if (result == null) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Frame processing returned null result',
              'Time: ${processingTime.inMilliseconds}ms',
            ),
          );
        }
      }).catchError((Object error) {
        final processingTime = DateTime.now().difference(frameStartTime);
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection frame processing failed',
            'Error: $error, Time: ${processingTime.inMilliseconds}ms',
          ),
        );
      }).whenComplete(() {
        _isProcessingFrame = false;
        final totalTime = DateTime.now().difference(frameStartTime);
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame processing completed',
            'Total time: ${totalTime.inMilliseconds}ms',
          ),
        );
      });
    }).catchError((Object error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'AnalysisImage conversion failed',
          'Error: $error',
        ),
      );
      _isProcessingFrame = false;
    });
  }

  /// Converts CamerAwesome AnalysisImage to ML Kit InputImage
  Future<InputImage?> _convertAnalysisImageToInputImage(
    AnalysisImage analysisImage,
  ) async {
    try {
      return analysisImage.when(
        nv21: (nv21Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting NV21 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          // CRITICAL FIX: Calculate proper bytesPerRow for NV21 format
          // NV21 Y plane has width stride, UV plane has width stride
          final yPlaneSize = analysisImage.width * analysisImage.height;
          final uvPlaneSize = (analysisImage.width * analysisImage.height) ~/ 2;
          final expectedSize = yPlaneSize + uvPlaneSize;

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'NV21 format validation',
              'Expected size: $expectedSize, '
                  'Actual size: ${nv21Image.bytes.length}',
            ),
          );

          return InputImage.fromBytes(
            bytes: nv21Image.bytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.nv21,
              bytesPerRow: analysisImage.width, // Y plane stride
            ),
          );
        },
        bgra8888: (bgra8888Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting BGRA8888 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          return InputImage.fromBytes(
            bytes: bgra8888Image.bytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.bgra8888,
              bytesPerRow: analysisImage.width * 4,
            ),
          );
        },
        yuv420: (yuv420Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting YUV420 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}, '
                  'Planes: ${yuv420Image.planes.length}',
            ),
          );

          // CRITICAL FIX: Improved YUV420 plane combination
          final combinedBytes = _combineYUV420PlanesImproved(yuv420Image);

          if (combinedBytes.isEmpty) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'YUV420 plane combination failed',
                'Unable to combine planes properly',
              ),
            );
            // Return null to indicate conversion failure
            throw Exception('YUV420 plane combination failed');
          }

          return InputImage.fromBytes(
            bytes: combinedBytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.yuv420,
              bytesPerRow: analysisImage.width,
            ),
          );
        },
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'AnalysisImage to InputImage conversion failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Combines YUV420 planes into a single byte array with improved handling
  Uint8List _combineYUV420PlanesImproved(Yuv420Image yuv420Image) {
    try {
      final planes = yuv420Image.planes;

      if (planes.isEmpty) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'YUV420 planes empty',
            'No planes available for combination',
          ),
        );
        return Uint8List(0);
      }

      final allBytes = <int>[];

      // Combine all plane bytes with validation
      for (var i = 0; i < planes.length; i++) {
        final plane = planes[i];
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Processing YUV420 plane $i',
            'Bytes length: ${plane.bytes.length}',
          ),
        );
        allBytes.addAll(plane.bytes);
      }

      final result = Uint8List.fromList(allBytes);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 planes combined successfully',
          'Total bytes: ${result.length}, Planes: ${planes.length}',
        ),
      );

      return result;
    } catch (error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 plane combination error',
          'Error: $error',
        ),
      );
      return Uint8List(0);
    }
  }
}
